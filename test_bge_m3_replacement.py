#!/usr/bin/env python3
"""
BGE-M3 替换验证测试脚本
验证从 gte-large 到 BGE-M3 的替换是否成功
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_config_validation():
    """测试配置验证"""
    print("🔧 测试配置验证...")
    try:
        from config import validate_bge_m3_config, settings
        
        print(f"   模型名称: {settings.EMBEDDING_MODEL_NAME}")
        print(f"   最大序列长度: {settings.EMBEDDING_MAX_LENGTH}")
        print(f"   批处理大小: {settings.EMBEDDING_BATCH_SIZE}")
        
        is_valid = validate_bge_m3_config()
        if is_valid:
            print("   ✅ 配置验证通过")
        else:
            print("   ❌ 配置验证失败")
        return is_valid
        
    except Exception as e:
        print(f"   ❌ 配置验证异常: {e}")
        return False


def test_device_utils():
    """测试设备工具"""
    print("\n🖥️  测试设备工具...")
    try:
        from src.device_utils import get_optimal_device, get_device_info, recommend_device_settings
        
        device = get_optimal_device()
        print(f"   最优设备: {device}")
        
        device_info = get_device_info(device)
        print(f"   设备可用: {device_info['available']}")
        print(f"   PyTorch可用: {device_info['torch_available']}")
        
        recommendations = recommend_device_settings()
        print(f"   推荐使用半精度: {recommendations['use_fp16']}")
        print(f"   推荐批处理大小: {recommendations['batch_size']}")
        
        print("   ✅ 设备工具测试通过")
        return True
        
    except Exception as e:
        print(f"   ❌ 设备工具测试失败: {e}")
        return False


def test_embeddings_import():
    """测试嵌入服务导入"""
    print("\n📦 测试嵌入服务导入...")
    try:
        from src.embeddings import BGEM3Embeddings, create_embeddings
        
        print("   ✅ BGEM3Embeddings 导入成功")
        print("   ✅ create_embeddings 函数导入成功")
        
        # 测试向后兼容性
        from src.embeddings import GTELargeEmbeddings
        print("   ✅ GTELargeEmbeddings 别名可用（向后兼容）")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 嵌入服务导入失败: {e}")
        return False


def test_embeddings_initialization():
    """测试嵌入服务初始化（不加载模型）"""
    print("\n🚀 测试嵌入服务初始化...")
    try:
        from src.embeddings import BGEM3Embeddings
        
        # 只测试初始化，不加载模型
        embeddings = BGEM3Embeddings.__new__(BGEM3Embeddings)
        
        # 手动设置一些基本属性来测试
        embeddings.model_name = "BAAI/bge-m3"
        embeddings.max_length = 8192
        embeddings.device = "cpu"
        embeddings.normalize_embeddings = True
        
        # 测试缓存键生成
        cache_key = embeddings._get_cache_key("测试文本")
        print(f"   缓存键生成: {cache_key[:16]}...")
        
        print("   ✅ 嵌入服务初始化测试通过")
        return True
        
    except Exception as e:
        print(f"   ❌ 嵌入服务初始化失败: {e}")
        return False


def test_requirements():
    """测试依赖包"""
    print("\n📋 测试依赖包...")
    
    required_packages = [
        ("FlagEmbedding", "BGE-M3模型库"),
        ("transformers", "Transformers库"),
        ("torch", "PyTorch"),
        ("numpy", "NumPy"),
        ("langchain_core", "LangChain核心"),
    ]
    
    all_available = True
    
    for package, description in required_packages:
        try:
            __import__(package)
            print(f"   ✅ {description} ({package}) 可用")
        except ImportError:
            print(f"   ❌ {description} ({package}) 不可用")
            all_available = False
    
    return all_available


def test_model_download_script():
    """测试模型下载脚本"""
    print("\n📥 测试模型下载脚本...")
    try:
        # 只导入，不执行下载
        import models.download_models as download_module
        
        downloader = download_module.ModelDownloader()
        print(f"   模型名称: {downloader.model_name}")
        print(f"   模型路径: {downloader.model_path}")
        print(f"   旧模型路径: {downloader.old_model_paths}")
        
        print("   ✅ 模型下载脚本测试通过")
        return True
        
    except Exception as e:
        print(f"   ❌ 模型下载脚本测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🧪 BGE-M3 替换验证测试")
    print("=" * 50)
    
    tests = [
        ("配置验证", test_config_validation),
        ("设备工具", test_device_utils),
        ("嵌入服务导入", test_embeddings_import),
        ("嵌入服务初始化", test_embeddings_initialization),
        ("依赖包检查", test_requirements),
        ("模型下载脚本", test_model_download_script),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"   💥 {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！BGE-M3 替换成功")
        print("\n📝 下一步:")
        print("   1. 运行: python models/download_models.py")
        print("   2. 测试实际模型加载和嵌入生成")
        print("   3. 运行主程序验证完整功能")
    else:
        print("⚠️  部分测试失败，请检查相关配置")
        
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
