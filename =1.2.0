Collecting FlagEmbedding
  Downloading FlagEmbedding-1.3.5.tar.gz (163 kB)
  Preparing metadata (setup.py): started
  Preparing metadata (setup.py): finished with status 'done'
Requirement already satisfied: torch>=1.6.0 in /Users/<USER>/anaconda3/envs/langchain_env/lib/python3.10/site-packages (from FlagEmbedding) (2.2.2)
Requirement already satisfied: transformers>=4.44.2 in /Users/<USER>/anaconda3/envs/langchain_env/lib/python3.10/site-packages (from FlagEmbedding) (4.52.4)
Collecting datasets>=2.19.0 (from FlagEmbedding)
  Downloading datasets-3.6.0-py3-none-any.whl.metadata (19 kB)
Requirement already satisfied: accelerate>=0.20.1 in /Users/<USER>/anaconda3/envs/langchain_env/lib/python3.10/site-packages (from FlagEmbedding) (1.8.0)
Requirement already satisfied: sentence_transformers in /Users/<USER>/anaconda3/envs/langchain_env/lib/python3.10/site-packages (from FlagEmbedding) (4.1.0)
Collecting peft (from FlagEmbedding)
  Downloading peft-0.15.2-py3-none-any.whl.metadata (13 kB)
Collecting ir-datasets (from FlagEmbedding)
  Downloading ir_datasets-0.5.10-py3-none-any.whl.metadata (12 kB)
Collecting sentencepiece (from FlagEmbedding)
  Downloading sentencepiece-0.2.0-cp310-cp310-macosx_10_9_x86_64.whl.metadata (7.7 kB)
Requirement already satisfied: protobuf in /Users/<USER>/anaconda3/envs/langchain_env/lib/python3.10/site-packages (from FlagEmbedding) (5.29.5)
Requirement already satisfied: numpy<3.0.0,>=1.17 in /Users/<USER>/anaconda3/envs/langchain_env/lib/python3.10/site-packages (from accelerate>=0.20.1->FlagEmbedding) (1.26.4)
Requirement already satisfied: packaging>=20.0 in /Users/<USER>/anaconda3/envs/langchain_env/lib/python3.10/site-packages (from accelerate>=0.20.1->FlagEmbedding) (24.2)
Requirement already satisfied: psutil in /Users/<USER>/anaconda3/envs/langchain_env/lib/python3.10/site-packages (from accelerate>=0.20.1->FlagEmbedding) (7.0.0)
Requirement already satisfied: pyyaml in /Users/<USER>/anaconda3/envs/langchain_env/lib/python3.10/site-packages (from accelerate>=0.20.1->FlagEmbedding) (6.0.2)
Requirement already satisfied: huggingface_hub>=0.21.0 in /Users/<USER>/anaconda3/envs/langchain_env/lib/python3.10/site-packages (from accelerate>=0.20.1->FlagEmbedding) (0.33.0)
Requirement already satisfied: safetensors>=0.4.3 in /Users/<USER>/anaconda3/envs/langchain_env/lib/python3.10/site-packages (from accelerate>=0.20.1->FlagEmbedding) (0.5.3)
Requirement already satisfied: filelock in /Users/<USER>/anaconda3/envs/langchain_env/lib/python3.10/site-packages (from datasets>=2.19.0->FlagEmbedding) (3.18.0)
Collecting pyarrow>=15.0.0 (from datasets>=2.19.0->FlagEmbedding)
  Downloading pyarrow-20.0.0-cp310-cp310-macosx_12_0_x86_64.whl.metadata (3.3 kB)
Collecting dill<0.3.9,>=0.3.0 (from datasets>=2.19.0->FlagEmbedding)
  Downloading dill-0.3.8-py3-none-any.whl.metadata (10 kB)
Requirement already satisfied: pandas in /Users/<USER>/anaconda3/envs/langchain_env/lib/python3.10/site-packages (from datasets>=2.19.0->FlagEmbedding) (2.3.0)
Requirement already satisfied: requests>=2.32.2 in /Users/<USER>/anaconda3/envs/langchain_env/lib/python3.10/site-packages (from datasets>=2.19.0->FlagEmbedding) (2.32.4)
Requirement already satisfied: tqdm>=4.66.3 in /Users/<USER>/anaconda3/envs/langchain_env/lib/python3.10/site-packages (from datasets>=2.19.0->FlagEmbedding) (4.67.1)
Collecting xxhash (from datasets>=2.19.0->FlagEmbedding)
  Downloading xxhash-3.5.0-cp310-cp310-macosx_10_9_x86_64.whl.metadata (12 kB)
Collecting multiprocess<0.70.17 (from datasets>=2.19.0->FlagEmbedding)
  Downloading multiprocess-0.70.16-py310-none-any.whl.metadata (7.2 kB)
Collecting fsspec<=2025.3.0,>=2023.1.0 (from fsspec[http]<=2025.3.0,>=2023.1.0->datasets>=2.19.0->FlagEmbedding)
  Downloading fsspec-2025.3.0-py3-none-any.whl.metadata (11 kB)
Requirement already satisfied: aiohttp!=4.0.0a0,!=4.0.0a1 in /Users/<USER>/anaconda3/envs/langchain_env/lib/python3.10/site-packages (from fsspec[http]<=2025.3.0,>=2023.1.0->datasets>=2.19.0->FlagEmbedding) (3.12.13)
Requirement already satisfied: aiohappyeyeballs>=2.5.0 in /Users/<USER>/anaconda3/envs/langchain_env/lib/python3.10/site-packages (from aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]<=2025.3.0,>=2023.1.0->datasets>=2.19.0->FlagEmbedding) (2.6.1)
Requirement already satisfied: aiosignal>=1.1.2 in /Users/<USER>/anaconda3/envs/langchain_env/lib/python3.10/site-packages (from aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]<=2025.3.0,>=2023.1.0->datasets>=2.19.0->FlagEmbedding) (1.3.2)
Requirement already satisfied: async-timeout<6.0,>=4.0 in /Users/<USER>/anaconda3/envs/langchain_env/lib/python3.10/site-packages (from aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]<=2025.3.0,>=2023.1.0->datasets>=2.19.0->FlagEmbedding) (4.0.3)
Requirement already satisfied: attrs>=17.3.0 in /Users/<USER>/anaconda3/envs/langchain_env/lib/python3.10/site-packages (from aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]<=2025.3.0,>=2023.1.0->datasets>=2.19.0->FlagEmbedding) (25.3.0)
Requirement already satisfied: frozenlist>=1.1.1 in /Users/<USER>/anaconda3/envs/langchain_env/lib/python3.10/site-packages (from aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]<=2025.3.0,>=2023.1.0->datasets>=2.19.0->FlagEmbedding) (1.7.0)
Requirement already satisfied: multidict<7.0,>=4.5 in /Users/<USER>/anaconda3/envs/langchain_env/lib/python3.10/site-packages (from aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]<=2025.3.0,>=2023.1.0->datasets>=2.19.0->FlagEmbedding) (6.5.0)
Requirement already satisfied: propcache>=0.2.0 in /Users/<USER>/anaconda3/envs/langchain_env/lib/python3.10/site-packages (from aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]<=2025.3.0,>=2023.1.0->datasets>=2.19.0->FlagEmbedding) (0.3.2)
Requirement already satisfied: yarl<2.0,>=1.17.0 in /Users/<USER>/anaconda3/envs/langchain_env/lib/python3.10/site-packages (from aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]<=2025.3.0,>=2023.1.0->datasets>=2.19.0->FlagEmbedding) (1.20.1)
Requirement already satisfied: typing-extensions>=4.1.0 in /Users/<USER>/anaconda3/envs/langchain_env/lib/python3.10/site-packages (from multidict<7.0,>=4.5->aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]<=2025.3.0,>=2023.1.0->datasets>=2.19.0->FlagEmbedding) (4.14.0)
Requirement already satisfied: idna>=2.0 in /Users/<USER>/anaconda3/envs/langchain_env/lib/python3.10/site-packages (from yarl<2.0,>=1.17.0->aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]<=2025.3.0,>=2023.1.0->datasets>=2.19.0->FlagEmbedding) (3.10)
Requirement already satisfied: hf-xet<2.0.0,>=1.1.2 in /Users/<USER>/anaconda3/envs/langchain_env/lib/python3.10/site-packages (from huggingface_hub>=0.21.0->accelerate>=0.20.1->FlagEmbedding) (1.1.4)
Requirement already satisfied: charset_normalizer<4,>=2 in /Users/<USER>/anaconda3/envs/langchain_env/lib/python3.10/site-packages (from requests>=2.32.2->datasets>=2.19.0->FlagEmbedding) (3.4.2)
Requirement already satisfied: urllib3<3,>=1.21.1 in /Users/<USER>/anaconda3/envs/langchain_env/lib/python3.10/site-packages (from requests>=2.32.2->datasets>=2.19.0->FlagEmbedding) (2.5.0)
Requirement already satisfied: certifi>=2017.4.17 in /Users/<USER>/anaconda3/envs/langchain_env/lib/python3.10/site-packages (from requests>=2.32.2->datasets>=2.19.0->FlagEmbedding) (2025.6.15)
Requirement already satisfied: sympy in /Users/<USER>/anaconda3/envs/langchain_env/lib/python3.10/site-packages (from torch>=1.6.0->FlagEmbedding) (1.14.0)
Requirement already satisfied: networkx in /Users/<USER>/anaconda3/envs/langchain_env/lib/python3.10/site-packages (from torch>=1.6.0->FlagEmbedding) (3.4.2)
Requirement already satisfied: jinja2 in /Users/<USER>/anaconda3/envs/langchain_env/lib/python3.10/site-packages (from torch>=1.6.0->FlagEmbedding) (3.1.6)
Requirement already satisfied: regex!=2019.12.17 in /Users/<USER>/anaconda3/envs/langchain_env/lib/python3.10/site-packages (from transformers>=4.44.2->FlagEmbedding) (2024.11.6)
Requirement already satisfied: tokenizers<0.22,>=0.21 in /Users/<USER>/anaconda3/envs/langchain_env/lib/python3.10/site-packages (from transformers>=4.44.2->FlagEmbedding) (0.21.1)
Requirement already satisfied: beautifulsoup4>=4.4.1 in /Users/<USER>/anaconda3/envs/langchain_env/lib/python3.10/site-packages (from ir-datasets->FlagEmbedding) (4.13.4)
Collecting inscriptis>=2.2.0 (from ir-datasets->FlagEmbedding)
  Downloading inscriptis-2.6.0-py3-none-any.whl.metadata (25 kB)
Requirement already satisfied: lxml>=4.5.2 in /Users/<USER>/anaconda3/envs/langchain_env/lib/python3.10/site-packages (from ir-datasets->FlagEmbedding) (5.4.0)
Collecting trec-car-tools>=2.5.4 (from ir-datasets->FlagEmbedding)
  Downloading trec_car_tools-2.6-py3-none-any.whl.metadata (640 bytes)
Collecting lz4>=3.1.10 (from ir-datasets->FlagEmbedding)
  Downloading lz4-4.4.4-cp310-cp310-macosx_10_9_x86_64.whl.metadata (3.8 kB)
Collecting warc3-wet>=0.2.3 (from ir-datasets->FlagEmbedding)
  Downloading warc3_wet-0.2.5-py3-none-any.whl.metadata (2.2 kB)
Collecting warc3-wet-clueweb09>=0.2.5 (from ir-datasets->FlagEmbedding)
  Downloading warc3-wet-clueweb09-0.2.5.tar.gz (17 kB)
  Preparing metadata (setup.py): started
  Preparing metadata (setup.py): finished with status 'done'
Collecting zlib-state>=0.1.3 (from ir-datasets->FlagEmbedding)
  Downloading zlib_state-0.1.9.tar.gz (9.5 kB)
  Installing build dependencies: started
  Installing build dependencies: finished with status 'done'
  Getting requirements to build wheel: started
  Getting requirements to build wheel: finished with status 'done'
  Preparing metadata (pyproject.toml): started
  Preparing metadata (pyproject.toml): finished with status 'done'
Collecting ijson>=3.1.3 (from ir-datasets->FlagEmbedding)
  Downloading ijson-3.4.0-cp310-cp310-macosx_10_9_x86_64.whl.metadata (21 kB)
Collecting unlzw3>=0.2.1 (from ir-datasets->FlagEmbedding)
  Downloading unlzw3-0.2.3-py3-none-any.whl.metadata (2.3 kB)
Requirement already satisfied: soupsieve>1.2 in /Users/<USER>/anaconda3/envs/langchain_env/lib/python3.10/site-packages (from beautifulsoup4>=4.4.1->ir-datasets->FlagEmbedding) (2.7)
Collecting cbor>=1.0.0 (from trec-car-tools>=2.5.4->ir-datasets->FlagEmbedding)
  Downloading cbor-1.0.0.tar.gz (20 kB)
  Preparing metadata (setup.py): started
  Preparing metadata (setup.py): finished with status 'done'
Requirement already satisfied: MarkupSafe>=2.0 in /Users/<USER>/anaconda3/envs/langchain_env/lib/python3.10/site-packages (from jinja2->torch>=1.6.0->FlagEmbedding) (3.0.2)
Requirement already satisfied: python-dateutil>=2.8.2 in /Users/<USER>/anaconda3/envs/langchain_env/lib/python3.10/site-packages (from pandas->datasets>=2.19.0->FlagEmbedding) (2.9.0.post0)
Requirement already satisfied: pytz>=2020.1 in /Users/<USER>/anaconda3/envs/langchain_env/lib/python3.10/site-packages (from pandas->datasets>=2.19.0->FlagEmbedding) (2025.2)
Requirement already satisfied: tzdata>=2022.7 in /Users/<USER>/anaconda3/envs/langchain_env/lib/python3.10/site-packages (from pandas->datasets>=2.19.0->FlagEmbedding) (2025.2)
Requirement already satisfied: six>=1.5 in /Users/<USER>/anaconda3/envs/langchain_env/lib/python3.10/site-packages (from python-dateutil>=2.8.2->pandas->datasets>=2.19.0->FlagEmbedding) (1.17.0)
Requirement already satisfied: scikit-learn in /Users/<USER>/anaconda3/envs/langchain_env/lib/python3.10/site-packages (from sentence_transformers->FlagEmbedding) (1.7.0)
Requirement already satisfied: scipy in /Users/<USER>/anaconda3/envs/langchain_env/lib/python3.10/site-packages (from sentence_transformers->FlagEmbedding) (1.15.3)
Requirement already satisfied: Pillow in /Users/<USER>/anaconda3/envs/langchain_env/lib/python3.10/site-packages (from sentence_transformers->FlagEmbedding) (11.2.1)
Requirement already satisfied: joblib>=1.2.0 in /Users/<USER>/anaconda3/envs/langchain_env/lib/python3.10/site-packages (from scikit-learn->sentence_transformers->FlagEmbedding) (1.5.1)
Requirement already satisfied: threadpoolctl>=3.1.0 in /Users/<USER>/anaconda3/envs/langchain_env/lib/python3.10/site-packages (from scikit-learn->sentence_transformers->FlagEmbedding) (3.6.0)
Requirement already satisfied: mpmath<1.4,>=1.1.0 in /Users/<USER>/anaconda3/envs/langchain_env/lib/python3.10/site-packages (from sympy->torch>=1.6.0->FlagEmbedding) (1.3.0)
Downloading datasets-3.6.0-py3-none-any.whl (491 kB)
Downloading dill-0.3.8-py3-none-any.whl (116 kB)
Downloading fsspec-2025.3.0-py3-none-any.whl (193 kB)
Downloading multiprocess-0.70.16-py310-none-any.whl (134 kB)
Downloading pyarrow-20.0.0-cp310-cp310-macosx_12_0_x86_64.whl (32.3 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 32.3/32.3 MB 7.8 MB/s eta 0:00:00
Downloading ir_datasets-0.5.10-py3-none-any.whl (859 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 859.0/859.0 kB 1.8 MB/s eta 0:00:00
Downloading ijson-3.4.0-cp310-cp310-macosx_10_9_x86_64.whl (59 kB)
Downloading inscriptis-2.6.0-py3-none-any.whl (45 kB)
Downloading lz4-4.4.4-cp310-cp310-macosx_10_9_x86_64.whl (220 kB)
Downloading trec_car_tools-2.6-py3-none-any.whl (8.4 kB)
Downloading unlzw3-0.2.3-py3-none-any.whl (6.7 kB)
Downloading warc3_wet-0.2.5-py3-none-any.whl (18 kB)
Downloading peft-0.15.2-py3-none-any.whl (411 kB)
Downloading sentencepiece-0.2.0-cp310-cp310-macosx_10_9_x86_64.whl (1.2 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 1.2/1.2 MB 3.5 MB/s eta 0:00:00
Downloading xxhash-3.5.0-cp310-cp310-macosx_10_9_x86_64.whl (31 kB)
Building wheels for collected packages: FlagEmbedding, cbor, warc3-wet-clueweb09, zlib-state
  Building wheel for FlagEmbedding (setup.py): started
  Building wheel for FlagEmbedding (setup.py): finished with status 'done'
  Created wheel for FlagEmbedding: filename=flagembedding-1.3.5-py3-none-any.whl size=233820 sha256=f131b19d8562198412368e6f563d9c0890d1144a9b0cc4fe540415ca9a91c2a9
  Stored in directory: /Users/<USER>/Library/Caches/pip/wheels/1a/de/99/8b79fb66f840ade80010d5aabfd20ace4f3f197d5baed65690
  Building wheel for cbor (setup.py): started
  Building wheel for cbor (setup.py): finished with status 'done'
  Created wheel for cbor: filename=cbor-1.0.0-cp310-cp310-macosx_10_15_x86_64.whl size=19924 sha256=671539db18d2ef902db876e6f29d13242009de97758d0a2d4aa2d55d70f3218e
  Stored in directory: /Users/<USER>/Library/Caches/pip/wheels/85/df/c9/b39e40eccaf76dbd218556639a6dc81562226f4c6a64902c85
  Building wheel for warc3-wet-clueweb09 (setup.py): started
  Building wheel for warc3-wet-clueweb09 (setup.py): finished with status 'done'
  Created wheel for warc3-wet-clueweb09: filename=warc3_wet_clueweb09-0.2.5-py3-none-any.whl size=18997 sha256=ab14e1db77c026214a3d09ff857760e9ccbcbc86d20e09e4e24ac364769df17b
  Stored in directory: /Users/<USER>/Library/Caches/pip/wheels/1a/d7/91/7ffb991df87e62355d945745035470ba2616aa3d83a250b5f9
  Building wheel for zlib-state (pyproject.toml): started
  Building wheel for zlib-state (pyproject.toml): finished with status 'done'
  Created wheel for zlib-state: filename=zlib_state-0.1.9-cp310-cp310-macosx_10_15_x86_64.whl size=9295 sha256=193d7d538f1c9234f23a1d511b22893f6569ca0689f4293cebb269263e606c66
  Stored in directory: /Users/<USER>/Library/Caches/pip/wheels/13/93/f4/84a615b37966a45c8694b2adf16580c849ea3c8807e73bfba2
Successfully built FlagEmbedding cbor warc3-wet-clueweb09 zlib-state
Installing collected packages: warc3-wet-clueweb09, warc3-wet, sentencepiece, cbor, zlib-state, xxhash, unlzw3, trec-car-tools, pyarrow, lz4, ijson, fsspec, dill, multiprocess, inscriptis, ir-datasets, peft, datasets, FlagEmbedding
  Attempting uninstall: fsspec
    Found existing installation: fsspec 2025.5.1
    Uninstalling fsspec-2025.5.1:
      Successfully uninstalled fsspec-2025.5.1

Successfully installed FlagEmbedding-1.3.5 cbor-1.0.0 datasets-3.6.0 dill-0.3.8 fsspec-2025.3.0 ijson-3.4.0 inscriptis-2.6.0 ir-datasets-0.5.10 lz4-4.4.4 multiprocess-0.70.16 peft-0.15.2 pyarrow-20.0.0 sentencepiece-0.2.0 trec-car-tools-2.6 unlzw3-0.2.3 warc3-wet-0.2.5 warc3-wet-clueweb09-0.2.5 xxhash-3.5.0 zlib-state-0.1.9
