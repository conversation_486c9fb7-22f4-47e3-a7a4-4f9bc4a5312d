"""
LLM模块 - 统一的LLM接口和工厂
"""

from .base import (
    BaseLLM,
    LLMConfig,
    LLMResponse,
    LLMProvider,
    LLMError,
    LLMConnectionError,
    LLMAuthenticationError,
    LLMRateLimitError,
    LLMInvalidRequestError,
)

from .factory import (
    LLMFactory,
    create_llm_from_settings,
    register_llm_provider,
)

from .langchain_adapter import (
    LangChainLLMAdapter,
    create_langchain_llm,
)

# 导入所有提供商以确保它们被注册
from . import providers

__all__ = [
    # 基础类和枚举
    'BaseLLM',
    'LLMConfig',
    'LLMResponse',
    'LLMProvider',

    # 异常类
    'LLMError',
    'LLMConnectionError',
    'LLMAuthenticationError',
    'LLMRateLimitError',
    'LLMInvalidRequestError',

    # 工厂类和函数
    'LLMFactory',
    'create_llm_from_settings',
    'register_llm_provider',

    # LangChain适配器
    'LangChainLLMAdapter',
    'create_langchain_llm',
]
