"""
DeepSeek LLM提供商实现
"""

import logging
from typing import List, Any, Dict, Optional
import time

from langchain_deepseek import ChatDeepSeek
from langchain_core.messages import HumanMessage

from ..base import BaseLLM, LLMConfig, LLMResponse, LLMProvider
from ..base import LLMError, LLMConnectionError, LLMAuthenticationError, LLMRateLimitError
from ..factory import register_llm_provider

logger = logging.getLogger(__name__)


@register_llm_provider(LLMProvider.DEEPSEEK)
class DeepSeekLLM(BaseLLM):
    """DeepSeek LLM实现"""

    def _initialize_client(self):
        """初始化DeepSeek客户端"""
        try:
            if not self.config.api_key:
                raise LLMAuthenticationError("DeepSeek API密钥未设置")

            self._client = ChatDeepSeek(
                api_key=self.config.api_key,
                model=self.config.model,
                temperature=self.config.temperature,
                max_tokens=self.config.max_tokens,
                base_url=self.config.base_url,
                **self.config.extra_params
            )
            logger.debug(f"DeepSeek客户端初始化成功: {self.config.model}")
            
        except Exception as e:
            logger.error(f"DeepSeek客户端初始化失败: {str(e)}")
            raise LLMConnectionError(f"DeepSeek客户端初始化失败: {str(e)}") from e

    def invoke(self, prompt: str, **kwargs) -> LLMResponse:
        """
        调用DeepSeek生成响应
        
        Args:
            prompt: 输入提示
            **kwargs: 额外参数
            
        Returns:
            LLM响应
        """
        if not self._client:
            raise LLMError("DeepSeek客户端未初始化")

        # 合并配置参数和调用参数
        call_params = {
            'temperature': kwargs.get('temperature', self.config.temperature),
            'max_tokens': kwargs.get('max_tokens', self.config.max_tokens),
        }

        # 重试机制
        last_exception = None
        for attempt in range(self.config.max_retries):
            try:
                logger.debug(f"DeepSeek调用尝试 {attempt + 1}/{self.config.max_retries}")
                
                # 调用DeepSeek
                response = self._client.invoke(prompt, **call_params)
                
                # 构建响应对象
                llm_response = LLMResponse(
                    content=response.content,
                    model=self.config.model,
                    raw_response=response
                )
                
                # 尝试提取使用信息
                if hasattr(response, 'usage_metadata'):
                    llm_response.usage = response.usage_metadata
                elif hasattr(response, 'response_metadata'):
                    llm_response.usage = response.response_metadata.get('usage', {})
                
                logger.debug(f"DeepSeek调用成功，响应长度: {len(response.content)}")
                return llm_response
                
            except Exception as e:
                last_exception = e
                logger.warning(f"DeepSeek调用失败 (尝试 {attempt + 1}): {str(e)}")
                
                # 根据错误类型决定是否重试
                if "rate limit" in str(e).lower():
                    if attempt < self.config.max_retries - 1:
                        wait_time = 2 ** attempt  # 指数退避
                        logger.info(f"遇到速率限制，等待 {wait_time} 秒后重试")
                        time.sleep(wait_time)
                        continue
                    else:
                        raise LLMRateLimitError(f"DeepSeek速率限制: {str(e)}") from e
                elif "authentication" in str(e).lower() or "api key" in str(e).lower():
                    raise LLMAuthenticationError(f"DeepSeek认证失败: {str(e)}") from e
                elif attempt < self.config.max_retries - 1:
                    # 其他错误也重试
                    time.sleep(1)
                    continue
                else:
                    break

        # 所有重试都失败了
        raise LLMError(f"DeepSeek调用失败，已重试 {self.config.max_retries} 次: {str(last_exception)}") from last_exception

    def batch_invoke(self, prompts: List[str], **kwargs) -> List[LLMResponse]:
        """
        批量调用DeepSeek
        
        Args:
            prompts: 提示列表
            **kwargs: 额外参数
            
        Returns:
            响应列表
        """
        responses = []
        for i, prompt in enumerate(prompts):
            try:
                logger.debug(f"批量调用DeepSeek: {i+1}/{len(prompts)}")
                response = self.invoke(prompt, **kwargs)
                responses.append(response)
            except Exception as e:
                logger.error(f"批量调用第 {i+1} 个提示失败: {str(e)}")
                # 创建错误响应
                error_response = LLMResponse(
                    content=f"调用失败: {str(e)}",
                    model=self.config.model
                )
                responses.append(error_response)
        
        return responses

    def stream_invoke(self, prompt: str, **kwargs):
        """
        流式调用DeepSeek
        
        Args:
            prompt: 输入提示
            **kwargs: 额外参数
            
        Yields:
            流式响应片段
        """
        if not self._client:
            raise LLMError("DeepSeek客户端未初始化")

        try:
            # 合并配置参数和调用参数
            call_params = {
                'temperature': kwargs.get('temperature', self.config.temperature),
                'max_tokens': kwargs.get('max_tokens', self.config.max_tokens),
            }

            logger.debug("开始DeepSeek流式调用")
            
            # DeepSeek流式调用
            for chunk in self._client.stream(prompt, **call_params):
                if hasattr(chunk, 'content') and chunk.content:
                    yield chunk.content
                    
        except Exception as e:
            logger.error(f"DeepSeek流式调用失败: {str(e)}")
            raise LLMError(f"DeepSeek流式调用失败: {str(e)}") from e
