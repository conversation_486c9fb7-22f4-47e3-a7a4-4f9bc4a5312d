# Core LangChain packages
langchain~=0.3.25
langchain-core~=0.3.65
langchain-community~=0.3.25

# LLM integrations
langchain-deepseek==0.1.3
openai>=1.0.0

# Embedding and vector store packages
FlagEmbedding>=1.2.0
sentence-transformers==3.3.1  # 保留以支持其他功能
transformers==4.47.1
torch>=2.0.0
chromadb==0.5.23
huggingface-hub>=0.20.0  # BGE-M3模型下载需要

# Document processing (保留Excel处理和基本文档处理)
openpyxl==3.1.5
pypdf==5.1.0
python-docx==1.1.2
unstructured==0.16.9

# Text processing and splitting
tiktoken==0.8.0
langchain-text-splitters~=0.3.8

# HTTP and API clients
httpx==0.28.1
requests==2.32.3

# Configuration and environment
python-dotenv==1.0.1
pydantic==2.10.3
pydantic-settings~=2.9.1

# CLI and user interface
click~=8.2.1
rich~=14.0.0
tqdm==4.67.1

# Utilities
numpy==1.26.4

# Development and testing (可选)
# pytest==8.3.4
# black==24.10.0

scikit-learn~=1.7.0
pandas~=2.3.0