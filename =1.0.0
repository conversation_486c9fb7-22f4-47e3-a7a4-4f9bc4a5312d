Requirement already satisfied: openai in /Users/<USER>/anaconda3/envs/langchain_env/lib/python3.10/site-packages (1.88.0)
Requirement already satisfied: anyio<5,>=3.5.0 in /Users/<USER>/anaconda3/envs/langchain_env/lib/python3.10/site-packages (from openai) (4.9.0)
Requirement already satisfied: distro<2,>=1.7.0 in /Users/<USER>/anaconda3/envs/langchain_env/lib/python3.10/site-packages (from openai) (1.9.0)
Requirement already satisfied: httpx<1,>=0.23.0 in /Users/<USER>/anaconda3/envs/langchain_env/lib/python3.10/site-packages (from openai) (0.28.1)
Requirement already satisfied: jiter<1,>=0.4.0 in /Users/<USER>/anaconda3/envs/langchain_env/lib/python3.10/site-packages (from openai) (0.10.0)
Requirement already satisfied: pydantic<3,>=1.9.0 in /Users/<USER>/anaconda3/envs/langchain_env/lib/python3.10/site-packages (from openai) (2.11.7)
Requirement already satisfied: sniffio in /Users/<USER>/anaconda3/envs/langchain_env/lib/python3.10/site-packages (from openai) (1.3.1)
Requirement already satisfied: tqdm>4 in /Users/<USER>/anaconda3/envs/langchain_env/lib/python3.10/site-packages (from openai) (4.67.1)
Requirement already satisfied: typing-extensions<5,>=4.11 in /Users/<USER>/anaconda3/envs/langchain_env/lib/python3.10/site-packages (from openai) (4.14.0)
Requirement already satisfied: exceptiongroup>=1.0.2 in /Users/<USER>/anaconda3/envs/langchain_env/lib/python3.10/site-packages (from anyio<5,>=3.5.0->openai) (1.3.0)
Requirement already satisfied: idna>=2.8 in /Users/<USER>/anaconda3/envs/langchain_env/lib/python3.10/site-packages (from anyio<5,>=3.5.0->openai) (3.10)
Requirement already satisfied: certifi in /Users/<USER>/anaconda3/envs/langchain_env/lib/python3.10/site-packages (from httpx<1,>=0.23.0->openai) (2025.6.15)
Requirement already satisfied: httpcore==1.* in /Users/<USER>/anaconda3/envs/langchain_env/lib/python3.10/site-packages (from httpx<1,>=0.23.0->openai) (1.0.9)
Requirement already satisfied: h11>=0.16 in /Users/<USER>/anaconda3/envs/langchain_env/lib/python3.10/site-packages (from httpcore==1.*->httpx<1,>=0.23.0->openai) (0.16.0)
Requirement already satisfied: annotated-types>=0.6.0 in /Users/<USER>/anaconda3/envs/langchain_env/lib/python3.10/site-packages (from pydantic<3,>=1.9.0->openai) (0.7.0)
Requirement already satisfied: pydantic-core==2.33.2 in /Users/<USER>/anaconda3/envs/langchain_env/lib/python3.10/site-packages (from pydantic<3,>=1.9.0->openai) (2.33.2)
Requirement already satisfied: typing-inspection>=0.4.0 in /Users/<USER>/anaconda3/envs/langchain_env/lib/python3.10/site-packages (from pydantic<3,>=1.9.0->openai) (0.4.1)
